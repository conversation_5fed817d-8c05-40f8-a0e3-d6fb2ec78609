import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Store,
  TrendingUp,
  Package,
  Star,
  Clock,
  DollarSign,
  Eye,
  Plus,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle2,
  Timer,
  Truck,
  Play,
  Pause,
  MapPin,
  Phone,
  ShoppingBag,
  Zap,
  Award,
  Target,
  Sparkles,
  Crown,
  Flame,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  TrendingDown,

} from 'lucide-react';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';
import { useOrdersStore } from '../../stores/ordersStore';
import { useNavigate } from 'react-router-dom';

// Import suppliers data from local data directory
import { suppliersData } from '../../data/suppliersData';

// Modern Glass Card Component
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5' }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ y: -5, scale: 1.02 }}
    className={`relative backdrop-blur-xl bg-gradient-to-br ${gradient} border border-white/20 rounded-3xl shadow-2xl overflow-hidden ${className}`}
  >
    {/* Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-[shimmer_2s_infinite]" />
    {children}
  </motion.div>
);

// Floating Orb Component
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full blur-xl opacity-20 ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Metric Card Component
const MetricCard: React.FC<{
  icon: React.ComponentType<any>;
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  gradient: string;
}> = ({ icon: Icon, title, value, change, changeType = 'neutral', gradient }) => (
  <GlassCard gradient={gradient} className="p-6">
    <div className="flex items-center justify-between mb-4">
      <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
        <Icon size={24} className="text-white" />
      </div>
      {change && (
        <div className={`flex items-center gap-1 text-sm font-semibold ${
          changeType === 'positive' ? 'text-green-300' :
          changeType === 'negative' ? 'text-red-300' : 'text-gray-300'
        }`}>
          {changeType === 'positive' && <ArrowUp size={14} />}
          {changeType === 'negative' && <ArrowDown size={14} />}
          {change}
        </div>
      )}
    </div>
    <div className="text-white">
      <p className="text-3xl font-black mb-1">{value}</p>
      <p className="text-white/80 text-sm font-medium">{title}</p>
    </div>
  </GlassCard>
);

// Quick Action Button Component
const QuickActionButton: React.FC<{
  icon: React.ComponentType<any>;
  label: string;
  onClick: () => void;
  gradient: string;
}> = ({ icon: Icon, label, onClick, gradient }) => (
  <motion.button
    onClick={onClick}
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    className={`relative group p-6 bg-gradient-to-br ${gradient} rounded-2xl shadow-xl border border-white/20 overflow-hidden`}
  >
    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    <div className="relative z-10 text-center">
      <div className="mb-3 flex justify-center">
        <Icon size={32} className="text-white drop-shadow-lg" />
      </div>
      <p className="text-white font-bold text-sm">{label}</p>
    </div>
  </motion.button>
);

// Order Status Section Component
const OrderStatusSection: React.FC<{
  title: string;
  icon: React.ComponentType<any>;
  orders: any[];
  gradient: string;
  renderOrderCard: (order: any) => React.ReactNode;
}> = ({ title, icon: Icon, orders, gradient, renderOrderCard }) => (
  <GlassCard gradient={gradient} className="p-8">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
          <Icon size={28} className="text-white" />
        </div>
        <div>
          <h3 className="text-white text-2xl font-black">{title}</h3>
          <p className="text-white/80 text-sm font-medium">{orders.length} orders</p>
        </div>
      </div>
      {orders.length > 0 && (
        <div className="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2">
          <span className="text-white text-lg font-black">{orders.length}</span>
        </div>
      )}
    </div>

    <div className="space-y-4">
      <AnimatePresence>
        {orders.length > 0 ? (
          orders.slice(0, 2).map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {renderOrderCard(order)}
            </motion.div>
          ))
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <div className="bg-white/10 rounded-2xl p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <CheckCircle2 size={24} className="text-green-400" />
            </div>
            <p className="text-white/80 font-medium">All clear!</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  </GlassCard>
);

const SupplierHomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { orders } = useOrdersStore();

  const [storeOpen, setStoreOpen] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Find supplier data based on user's supplierId
  const supplierData = suppliersData.find((supplier) => supplier.id === user?.supplierId);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Filter orders by status (for now using all orders as mock data)
  const newOrders = orders.filter((o) => o.status === 'Pending');
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing');
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way');
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered');
  const allSupplierOrders = orders; // In real app, filter by supplierId

  // Calculate today's stats
  const today = new Date();
  const todayOrders = allSupplierOrders.filter(order => {
    const orderDate = new Date(order.createdAt || today);
    return orderDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const avgRating = 4.8; // This would come from reviews data
  const totalProducts = 45; // This would come from products data

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Preparing': return "bg-yellow-500";
      case 'On the Way': return "bg-orange-500";
      case 'Delivered': return "bg-green-500";
      default: return "bg-red-500";
    }
  };

  const renderOrderCard = (order: any) => (
    <motion.div
      key={order.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02, y: -2 }}
      className="group relative backdrop-blur-xl bg-white/10 border border-white/20 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden"
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

      <div className="relative z-10">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30">
              <Package size={16} className="text-white" />
            </div>
            <span className="text-white font-bold text-lg">#{order.id}</span>
          </div>
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={`px-3 py-1.5 rounded-xl text-xs font-bold text-white shadow-md ${getStatusColor(order.status)}`}
          >
            {order.status}
          </motion.span>
        </div>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-white/90">
            <div className="p-1.5 bg-white/20 rounded-lg">
              <Package size={12} className="text-white" />
            </div>
            <span className="text-sm font-medium">{order.items.length} items</span>
          </div>
          <div className="flex items-center gap-2 text-white/90">
            <div className="p-1.5 bg-white/20 rounded-lg">
              <DollarSign size={12} className="text-white" />
            </div>
            <span className="text-sm font-medium">₪{order.total.toFixed(2)}</span>
          </div>
          <div className="flex items-center gap-2 text-white/90">
            <div className="p-1.5 bg-white/20 rounded-lg">
              <MapPin size={12} className="text-white" />
            </div>
            <span className="text-sm font-medium truncate">{order.address || 'No address'}</span>
          </div>
          <div className="flex items-center gap-2 text-white/90">
            <div className="p-1.5 bg-white/20 rounded-lg">
              <Phone size={12} className="text-white" />
            </div>
            <span className="text-sm font-medium">{order.phone}</span>
          </div>
        </div>

        <motion.button
          onClick={() => navigate(`/supplier/order-details/${order.id}`)}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full px-4 py-3 bg-gradient-to-r from-purple-500/80 to-blue-500/80 backdrop-blur-sm text-white rounded-xl border border-white/30 hover:from-purple-600/90 hover:to-blue-600/90 transition-all duration-200 flex items-center justify-center gap-2 font-semibold shadow-lg"
        >
          <Eye size={16} />
          View Details
          <ChevronRight size={14} />
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(1deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
          50% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.6); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.05); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(20px, -20px) rotate(1deg); }
          50% { transform: translate(-15px, 15px) rotate(-1deg); }
          75% { transform: translate(15px, 10px) rotate(0.5deg); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>

      {/* Modern Animated Background */}
      <div className="min-h-screen w-full relative overflow-hidden">
        {/* Primary Dynamic Gradient Background */}
        <div
          className="absolute inset-0 w-full h-full bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"
          style={{
            backgroundSize: '400% 400%',
            animation: 'gradient-shift 20s ease infinite'
          }}
        />

        {/* Secondary Overlay */}
        <div
          className="absolute inset-0 w-full h-full bg-gradient-to-tr from-blue-800/30 via-transparent to-purple-800/30"
          style={{
            backgroundSize: '300% 300%',
            animation: 'gradient-shift 15s ease infinite reverse'
          }}
        />

        {/* Floating Orbs */}
        <FloatingOrb size={400} color="bg-purple-500" delay={0} duration={25} x="10%" y="10%" />
        <FloatingOrb size={350} color="bg-blue-500" delay={2} duration={30} x="70%" y="20%" />
        <FloatingOrb size={300} color="bg-pink-500" delay={4} duration={22} x="20%" y="60%" />
        <FloatingOrb size={280} color="bg-indigo-500" delay={6} duration={28} x="80%" y="70%" />
        <FloatingOrb size={250} color="bg-cyan-500" delay={8} duration={35} x="50%" y="40%" />

        {/* Animated Wave Overlays */}
        <div className="fixed inset-0 w-full h-full opacity-10">
          <div
            className="fixed inset-0 w-full bg-gradient-to-r from-transparent via-white/40 to-transparent"
            style={{ animation: 'wave 12s ease-in-out infinite' }}
          />
          <div
            className="fixed inset-0 w-full bg-gradient-to-l from-transparent via-purple-300/30 to-transparent"
            style={{ animation: 'wave 8s ease-in-out infinite reverse' }}
          />
        </div>

        {/* Sparkle Effects */}
        <div className="fixed inset-0 w-full h-full">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `sparkle ${3 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 3}s`,
                boxShadow: '0 0 8px rgba(255,255,255,0.8)'
              }}
            />
          ))}
        </div>

        {/* Mesh Gradient Overlay */}
        <div className="fixed inset-0 w-full h-full bg-gradient-to-br from-black/20 via-transparent to-black/20"></div>

        {/* Main Content Container */}
        <div className="relative z-10 min-h-screen w-full p-6 pb-20">
          <div className="w-full space-y-8">

            {/* Modern Header Section */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  {/* Store Icon with Status */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <div className="relative p-4 bg-white/20 backdrop-blur-sm border border-white/30 rounded-3xl">
                      <Store size={40} className="text-white" />

                      {/* Status Indicator */}
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4 }}
                        className={`absolute -top-2 -right-2 w-6 h-6 ${storeOpen ? 'bg-green-500' : 'bg-red-500'} rounded-full border-2 border-white flex items-center justify-center`}
                      >
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {storeOpen ? <CheckCircle2 size={12} className="text-white" /> : <AlertCircle size={12} className="text-white" />}
                        </motion.div>
                      </motion.div>
                    </div>

                    {/* Glow Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 to-blue-400/30 rounded-3xl blur-xl animate-pulse" />
                  </motion.div>

                  {/* Welcome Text */}
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-4xl font-black mb-2 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent"
                    >
                      Welcome back, {supplierData?.name || 'Supplier'}! 👋
                    </motion.h1>
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-center gap-4"
                    >
                      <div className={`flex items-center gap-2 px-3 py-1.5 ${storeOpen ? 'bg-green-500/20' : 'bg-red-500/20'} backdrop-blur-sm rounded-xl border border-white/20`}>
                        <div className={`w-2 h-2 ${storeOpen ? 'bg-green-400' : 'bg-red-400'} rounded-full animate-pulse`} />
                        <span className="text-white text-sm font-semibold">
                          {storeOpen ? 'OPEN' : 'CLOSED'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-xl border border-white/20">
                        <Clock size={14} className="text-white/80" />
                        <span className="text-white text-sm font-medium">
                          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Store Toggle Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setStoreOpen(!storeOpen)}
                  className="group relative p-4 bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl hover:bg-white/30 transition-all duration-300"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="relative z-10">
                    {storeOpen ? (
                      <Pause size={24} className="text-white" />
                    ) : (
                      <Play size={24} className="text-white" />
                    )}
                  </div>
                </motion.button>
              </div>
            </GlassCard>

            {/* Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <MetricCard
                icon={TrendingUp}
                title="Today's Revenue"
                value={`₪${todayRevenue.toFixed(0)}`}
                change="+12%"
                changeType="positive"
                gradient="from-green-500/20 to-emerald-500/20"
              />
              <MetricCard
                icon={Package}
                title="Today's Orders"
                value={todayOrders.length}
                change={`${newOrders.length} pending`}
                changeType="neutral"
                gradient="from-blue-500/20 to-indigo-500/20"
              />
              <MetricCard
                icon={Star}
                title="Rating"
                value={avgRating}
                change="Excellent"
                changeType="positive"
                gradient="from-yellow-500/20 to-orange-500/20"
              />
            </div>

            {/* Quick Actions Section */}
            <GlassCard gradient="from-white/15 to-white/5" className="p-8 mt-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl">
                  <Zap size={24} className="text-white" />
                </div>
                <h3 className="text-white text-2xl font-black">Quick Actions</h3>
                <div className="flex-1 h-px bg-gradient-to-r from-white/30 to-transparent"></div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                <QuickActionButton
                  icon={Plus}
                  label="Add Product"
                  onClick={() => navigate('/supplier/products/add')}
                  gradient="from-green-500 to-emerald-600"
                />
                <QuickActionButton
                  icon={BarChart3}
                  label="Analytics"
                  onClick={() => navigate('/supplier/analytics')}
                  gradient="from-blue-500 to-indigo-600"
                />
                <QuickActionButton
                  icon={ShoppingBag}
                  label="Products"
                  onClick={() => navigate('/supplier/products')}
                  gradient="from-orange-500 to-red-600"
                />
                <QuickActionButton
                  icon={Settings}
                  label="Settings"
                  onClick={() => navigate('/supplier/profile')}
                  gradient="from-purple-500 to-pink-600"
                />
              </div>
            </GlassCard>

            {/* New Orders Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <OrderStatusSection
                title="🚨 New Orders"
                icon={AlertCircle}
                orders={newOrders}
                gradient="from-red-500/20 to-pink-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            {/* Performance Highlights */}
            <GlassCard gradient="from-white/15 to-white/5" className="p-8 mt-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                  <BarChart3 size={24} className="text-white" />
                </div>
                <h3 className="text-white text-2xl font-black">Today's Performance</h3>
                <div className="flex-1 h-px bg-gradient-to-r from-white/30 to-transparent"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <Target size={20} className="text-orange-400" />
                    <p className="text-white/80 text-sm font-bold">AVG ORDER</p>
                  </div>
                  <p className="text-white text-2xl font-black">
                    ₪{todayOrders.length > 0 ? (todayRevenue / todayOrders.length).toFixed(0) : '0'}
                  </p>
                </div>

                <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <CheckCircle2 size={20} className="text-green-400" />
                    <p className="text-white/80 text-sm font-bold">COMPLETION</p>
                  </div>
                  <p className="text-white text-2xl font-black">
                    {allSupplierOrders.length > 0
                      ? Math.round((deliveredOrders.length / allSupplierOrders.length) * 100)
                      : 0}%
                  </p>
                </div>

                <div className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <Package size={20} className="text-purple-400" />
                    <p className="text-white/80 text-sm font-bold">PRODUCTS</p>
                  </div>
                  <p className="text-white text-2xl font-black">{totalProducts}</p>
                </div>
              </div>
            </GlassCard>

            {/* Other Order Sections */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <OrderStatusSection
                title="In Preparing"
                icon={Timer}
                orders={inPreparingOrders}
                gradient="from-yellow-500/20 to-orange-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <OrderStatusSection
                title="On The Way"
                icon={Truck}
                orders={onTheWayOrders}
                gradient="from-orange-500/20 to-red-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <OrderStatusSection
                title="Delivered"
                icon={CheckCircle2}
                orders={deliveredOrders}
                gradient="from-green-500/20 to-emerald-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupplierHomePage;
